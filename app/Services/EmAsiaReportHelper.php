<?php

namespace App\Services;

use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

class EmAsiaReportHelper
{
    const COUNTRIES = [
        'HK' => 'Hong Kong',
        'ID' => 'Indonesia',
        'IN' => 'India',
        'MY' => 'Malaysia',
        'PH' => 'Philippines',
        'PK' => 'Pakistan',
        'SG' => 'Singapore',
        'TH' => 'Thailand',
        'TW' => 'Taiwan',
        'VN' => 'Vietnam',
        'AU' => 'Australia',
        'NZ' => 'New Zealand'
    ];
    const REGISTERED_COLUMNS = [
        'vendor' => '*Vendor',
        'webinar_owner_country' => '*Webinar Owner Country',
        'webinar_name' => '*Webinar Name',
        'webinar_date_time' => '*Webinar Date Time',
        'timezone_utc_offset' => '*Timezone UTC Offset',
        'webinar_duration' => '*Webinar Duration',
        'webinar_owner_name' => '*Webinar Owner Name',
        'webinar_owner_email_address' => '*Webinar Owner Email Address',
        'brand_1' => 'Brand_1(*)',
        'category_1' => 'Category/TA_1(*)',
        'brand_2' => 'Brand_2',
        'category_2' => 'Category/TA_2',
        'brand_3' => 'Brand_3',
        'category_3' => 'Category/TA_3',
        'brand_4' => 'Brand_4',
        'category_4' => 'Category/TA_4',
        'brand_5' => 'Brand_5',
        'category_5' => 'Category/TA_5',
        'brand_6' => 'Brand_6',
        'category_6' => 'Category/TA_6',
        'brand_7' => 'Brand_7',
        'category_7' => 'Category/TA_7',
        'brand_8' => 'Brand_8',
        'category_8' => 'Category/TA_8',
        'brand_9' => 'Brand_9',
        'category_9' => 'Category/TA_9',
        'brand_10' => 'Brand_10',
        'category_10' => 'Category/TA_10',
        'brand_11' => 'Brand_11',
        'category_11' => 'Category/TA_11',
        'brand_12' => 'Brand_12',
        'category_12' => 'Category/TA_12',
        'brand_13' => 'Brand_13',
        'category_13' => 'Category/TA_13',
        'brand_14' => 'Brand_14',
        'category_14' => 'Category/TA_14',
        'brand_15' => 'Brand_15',
        'category_15' => 'Category/TA_15',
        'brand_16' => 'Brand_16',
        'category_16' => 'Category/TA_16',
        'brand_17' => 'Brand_17',
        'category_17' => 'Category/TA_17',
        'brand_18' => 'Brand_18',
        'category_18' => 'Category/TA_18',
        'brand_19' => 'Brand_19',
        'category_19' => 'Category/TA_19',
        'brand_20' => 'Brand_20',
        'category_20' => 'Category/TA_20',
        'pfizer_webinar_unique_id' => 'Pfizer Webinar Unique ID',
        'webinar_reserved_field_1' => 'Webinar_Reserved_Field_1',
        'webinar_reserved_field_2' => 'Webinar_Reserved_Field_2',
        'webinar_reserved_field_3' => 'Webinar_Reserved_Field_3',
        'webinar_reserved_field_4' => 'Webinar_Reserved_Field_4',
        'webinar_reserved_field_5' => 'Webinar_Reserved_Field_5',
        'webinar_reserved_field_6' => 'Webinar_Reserved_Field_6',
        'webinar_reserved_field_7' => 'Webinar_Reserved_Field_7',
        'webinar_reserved_field_8' => 'Webinar_Reserved_Field_8',
        'webinar_reserved_field_9' => 'Webinar_Reserved_Field_9',
        'webinar_reserved_field_10' => 'Webinar_Reserved_Field_10',
        'grv_id' => 'GRV ID',
        'hcp_email_address' => '*HCP Email Address',
        'hcp_first_name' => 'HCP First Name',
        'hcp_last_name' => 'HCP Last Name',
        'hcp_full_name' => 'HCP Full Name',
        'hcp_registration_license_number' => 'HCP Registration / License Number',
        'hcp_phone_number' => 'HCP Phone Number',
        'hcp_specialty' => 'HCP Specialty',
        'hcp_address' => 'HCP Address',
        'hcp_city' => 'HCP City',
        'hcp_zip_code' => 'HCP Zip Code',
        'hcp_country' => 'HCP  Country',
        'sales_persons_email' => 'Sales Persons Email',
        'type_of_attendee' => 'Type of Attendee',
        'hcp_association_name' => 'HCP  Association Name',
        'hcp_organization_name' => 'HCP Organization Name',
        'hcp_organization_address' => 'HCP Organization Address',
        'secodary_participant' => 'Secodary Participant',
        'preferred_communication_tool' => 'Preferred Communication Tool',
        'go_ngo_classification' => 'GO/NGO Classification',
        'hcp_reserved_field_1' => 'HCP_Reserved_Field_1',
        'hcp_reserved_field_2' => 'HCP_Reserved_Field_2',
        'hcp_reserved_field_3' => 'HCP_Reserved_Field_3',
        'hcp_reserved_field_4' => 'HCP_Reserved_Field_4',
        'hcp_reserved_field_5' => 'HCP_Reserved_Field_5',
        'hcp_reserved_field_6' => 'HCP_Reserved_Field_6',
        'hcp_reserved_field_7' => 'HCP_Reserved_Field_7',
        'hcp_reserved_field_8' => 'HCP_Reserved_Field_8',
        'hcp_reserved_field_9' => 'HCP_Reserved_Field_9',
        'hcp_reserved_field_10' => 'HCP_Reserved_Field_10',
    ];

    const ATTENDED_COLUMNS = [
        'vendor' => '*Vendor',
        'webinar_owner_country' => '*Webinar Owner Country',
        'webinar_name' => '*Webinar Name',
        'webinar_date_time' => '*Webinar Date Time',
        'hcp_email_address' => '*HCP Email Address',
        'hcp_first_login_time' => '*HCP First Login Time',
        'hcp_attendance_total_duration' => '*HCP Attendance Total Duration (in min)',
        'survey_rating' => 'Survey Rating',
        'link_referral' => 'Link Referral',
        'reserved_field_1' => 'Reserved_Field_1',
        'reserved_field_2' => 'Reserved_Field_2',
        'reserved_field_3' => 'Reserved_Field_3',
        'reserved_field_4' => 'Reserved_Field_4',
        'reserved_field_5' => 'Reserved_Field_5',
        'reserved_field_6' => 'Reserved_Field_6',
        'reserved_field_7' => 'Reserved_Field_7',
        'reserved_field_8' => 'Reserved_Field_8',
        'reserved_field_9' => 'Reserved_Field_9',
        'reserved_field_10' => 'Reserved_Field_10'
    ];

    public function listOfRegisteredReport($vendor, $webinar_owner_country, $webinar_name, $webinar_date_time, $timezone_offset, $webinar_duration, $webinar_owner_name, $webinar_owner_email_address, $bc_list, $list)
    {
        $excel_helper = new ExcelHelper();
        $list = $excel_helper->readExcel($list->getRealPath());
        dd($list);
        $result = [];
        foreach ($list as $item) {
            $item['e-mail'] = $item['e-mail'] ?? $item['email'] ?? $item['e_mail'] ?? null;
            $row = [];
            foreach (self::REGISTERED_COLUMNS as $column => $label) {
                switch ($column) {
                    case "vendor":
                        $value = $vendor;
                        break;
                    case "webinar_owner_country":
                        $value = $webinar_owner_country;
                        break;
                    case "webinar_name":
                        $value = $webinar_name;
                        break;
                    case "webinar_date_time":
                        $value = date("m/d/Y H:i", strtotime(str_replace("/", "-", $webinar_date_time)));
                        break;
                    case "timezone_utc_offset":
                        $value = (string)$this->timezoneConvert($timezone_offset);
                        break;
                    case "webinar_duration":
                        $value = $webinar_duration;
                        break;
                    case "webinar_owner_name":
                        $value = $webinar_owner_name;
                        break;
                    case "webinar_owner_email_address":
                        $value = $webinar_owner_email_address;
                        break;

                    case "hcp_email_address":
                        $value = $item['e-mail'] ?? null;
                        break;
                    case "hcp_first_name":
                        $value = $item['first-name'];
                        break;
                    case "hcp_last_name":
                        $value = $item['last-name'];
                        break;
                    case "hcp_full_name":
                        $value = (!empty($item['fullname']) ? $item['fullname'] : ($item['first-name'] . ' ' . $item['last-name']));
                        break;
                    case "hcp_country":
                        $value = $this->getCountryISO($item['country']);
                        break;
                    case "hcp_specialty":
                        $value = trim($this->specialityCodeByCountry($item['speciality'], $this->getCountryISO($item['country'])));
                        break;
                    case "hcp_registration_license_number":
                        $value = $item['hcp-registration-no'] ?? null;
                        if (in_array($webinar_owner_country, ['AU', 'NZ'])) {
                            $value = $item['hcp-registration-no'] ?? Str::limit("dummyAHPRA" . $item['e-mail']);
                        }
                        break;
                    case "hcp_association_name":
                        $value = $item['hospital-clinic'] ?? null;
                        break;
                    case "hcp_reserved_field_1":
                        $value = null;
                        if (in_array($webinar_owner_country, ['AU', 'NZ'])) {
                            $value = $item['hcp-registration-no'] ?? Str::limit("dummyAHPRA" . $item['e-mail']);
                        }
                        break;

                    default:
                        $value = "";
                        break;
                }
                if (str_contains($column, 'brand_')) {
                    $bc_row = str_replace('brand_', '', $column);
                    $value = $bc_list[$bc_row]['brand'] ?? null;
                } else if (str_contains($column, 'category_')) {
                    $bc_row = str_replace('category_', '', $column);
                    $value = $bc_list[$bc_row]['category'] ?? null;
                }
                $row[$label] = $value;
            }
            $result[] = $row;
        }
        return $result;
    }

    public function listOfAttendedReport($vendor, $webinar_owner_country, $webinar_name, $webinar_date_time, $timezone_offset, $webinar_duration, $webinar_owner_name, $webinar_owner_email_address, $bc_list, $list, $registered_report = [])
    {
        $excel_helper = new ExcelHelper();
        $list = $excel_helper->readExcel($list->getRealPath());
        $result = [];
        foreach ($list as $item) {
            $item['e-mail'] = $item['e-mail'] ?? $item['email'] ?? $item['e_mail'] ?? null;
            $row = [];
            foreach (self::ATTENDED_COLUMNS as $column => $label) {
                switch ($column) {
                    case "vendor":
                        $value = $vendor;
                        break;
                    case "webinar_owner_country":
                        $value = $webinar_owner_country;
                        break;
                    case "webinar_name":
                        $value = $webinar_name;
                        break;
                    case "webinar_date_time":
                        $value = date("m/d/Y H:i", strtotime(str_replace("/", "-", $webinar_date_time)));
                        break;

                    case "hcp_email_address":
                        $value = $item['e-mail'] ?? null;
                        break;
                    case "hcp_first_login_time":
                        $value = $item['turkey-login-time'] ?? null;
                        break;
                    case "hcp_attendance_total_duration":
                        $value = $item['total-time-min'] ?? null;
                        break;

                    case "reserved_field_1":
                        $value = "";
                        if (in_array($webinar_owner_country, ['AU', 'NZ'])) {
                            $emails = array_column($registered_report, '*HCP Email Address');
                            $email_index = array_search($item['e-mail'], $emails);
                            if ($email_index !== false) {
                                $value = $registered_report[$email_index]['HCP Registration / License Number'] ?? "";
                            }
                        }
                        break;

                    default:
                        $value = "";
                        break;
                }

                $row[$label] = $value;
            }
            $result[] = $row;
        }
        return $result;
    }

    public function countryList()
    {
        return self::COUNTRIES;
    }

    public function specialityList($country = null)
    {
        if (!in_array($country, array_keys(self::COUNTRIES))) {
            return null;
        }
        $data = json_decode(file_get_contents(public_path('related/em-asia-report/specialityByCountry.json')), true);
        if (empty($country)) {
            return $data;
        }
        return array_values(array_filter($data, function ($item) use ($country) {
            return $country == $item['country_code'];
        }));
    }

    public function specialityCodeByCountry($speciality, $country)
    {
        $list = $this->specialityList($country);
        if (!empty($list)) {
            $search = array_search($speciality, array_column($list, 'speciality_desc1'));
            if ($search !== false) {
                return !empty($list[$search]['speciality_desc1']) ? $list[$search]['speciality_desc1'] : 'NA';
            } else {
                $search = array_search(strtoupper($speciality), array_column($list, 'speciality_desc1'));
                if ($search !== false) {
                    return !empty($list[$search]['speciality_desc1']) ? $list[$search]['speciality_desc1'] : 'NA';
                }
            }
        }
        return 'NA';
    }

    public function timezoneConvert($timezone)
    {
        $timezone_helper = new TimezoneHelper();
        $data = $timezone_helper->timezoneToTime($timezone);
        $data = explode(':', $data);
        $data = str_replace(['+'], '', reset($data));
        return (int)$data;
    }

    public function getCountryISO($country = null)
    {
        $data = json_decode(file_get_contents(public_path('related/em-asia-report/countryList.json')), true);
        if (empty($country)) {
            return $data;
        }
        $search = array_search($country, array_column($data, 'name'));
        return $search !== false ? $data[$search]['code'] : null;
    }
}
